use std::collections::HashSet;

use alloy::primitives::{Address, U256};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use crate::vira::{errors::DEXError, pool::{DexPool, POOL}, status::{display_util::display_mevs, mev::{Mev, MevStatus}}};


#[derive(Default, Serialize, Deserialize)]
pub struct Pools {
    pub data : DashMap<Address, POOL>,
    pub mevs : DashMap<Address, Vec<Mev>>,
}

impl Pools {
    pub fn new() -> Self {
        Self { data: DashMap::new(), mevs: DashMap::new() }
    }
    
    pub fn sync(&self, logs: &[alloy::rpc::types::Log]) -> Result<Vec<Address>, DEXError> {
        let mut affected_amms = HashSet::new();

        for log in logs {
            let address = log.address();
            if let Some(mut amm) = self.data.get_mut(&address) {
                affected_amms.insert(address);
                amm.sync(log)?;
            }
        }

        Ok(affected_amms.into_iter().collect())
    }

    pub fn display_mev_count(&self){
        // --- 新增统计信息 ---
        let mut total_mevs = 0;
        let mut with_fee = 0;
        let mut good = 0;
        let mut bad = 0;
        let mut single_good = 0;

        let mut print_bad = 0;
        let mut print_single = 0;

        for entry in self.mevs.iter() {
            let mev_list = entry.value();

            if mev_list.iter().any(|mev| mev.pools.iter().any(|p| p.fee > U256::ZERO || p.fee_desc > U256::ZERO)) {
                with_fee += 1;
            }

            // 检查状态
            for mev in mev_list.iter() {
                total_mevs += 1;
                if mev.status != mev.status_desc {
                    single_good += 1;

                    if print_single == 0 {
                        print_single = 1;
                        println!("\nsingle good mev: {:?}", entry.key());
                        display_mevs(mev_list, self);
                    }

                } else if mev.status == MevStatus::Active && mev.status_desc == MevStatus::Active {
                    good += 1;
                } else {
                    bad += 1;

                    if print_bad == 0 {
                        print_bad = 1;
                        println!("\nbad mev: {:?}", entry.key());
                        display_mevs(mev_list, self);
                    }
                }
            }
        }

        println!("\nmev统计信息:");
        println!("  - 存在mev的池子: {}", self.mevs.len());
        println!("  - 总mev数量:    {}", total_mevs);
        println!("  - 有fee的mev:   {}", with_fee);
        println!("  - good:        {}", good);
        println!("  - single_good: {}", single_good);
        println!("  - bad:         {}", bad);
        // --- 统计信息结束 ---
    }

    pub fn display_pools_count(&self){
        let total_pools = self.data.len();
        let mut status_counts = std::collections::HashMap::new();
        //let mut tokens_with_fee = 0;

        for entry in self.data.iter() {
            let pool = entry.value();
            let data = pool.data();

            // 统计不同状态的池数量
            *status_counts.entry(data.status.clone()).or_insert(0) += 1;

        }

        println!("\nPools统计信息:");
        println!("  - 总池数量: {}", total_pools);
        println!("  - 按状态分组统计:");
        for (status, count) in &status_counts {
            println!("    - {:?}: {}个", status, count);
        }
        //println!("  - 有手续费的代币总数量: {}", tokens_with_fee);
    }
}