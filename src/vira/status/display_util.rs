use std::borrow::Borrow;
use alloy::primitives::Address;
use alloy::transports::http::reqwest::dns::Addrs;
use colored::Colorize;
use dashmap::DashMap;

use crate::vira::pool::{DexPool, POOL};
use crate::vira::status::mev::MevStatus;
use crate::vira::status::pools::Pools;
use crate::vira::token::TokenManager;
use super::{mev::Mev, PoolIndex};

/// 显示工具
/// 提供格式化和展示功能

/// 打印 MEV 路径
pub fn display_mevs(mevs: &Vec<Mev>, pools: &Pools) {
    for mev in mevs {
        let mut str = vec![];
        let len = mev.pools.len();
        for (index, pool) in mev.pools.iter().enumerate() {
            let p = pools.data.get(&pool.addr).unwrap();
            let symbol = &p.data().tokens[pool.in_index].symbol;
            str.push(symbol.to_string());
            if index == len - 1 {
                let out_symbol = &p.data().tokens[pool.out_index].symbol;
                str.push(out_symbol.to_string());
            }
        }

        let stat_str = if mev.status_desc == MevStatus::Active {
                "Active".green()
            } else {
                "Bad".red()
            };

        let stat_desc_str = if mev.status == MevStatus::Active {
                "Active".green()
            } else {
                "Bad".red()
            };

        let stats_str = format!("[{},{}]", stat_str, stat_desc_str);

        let path_str = str.join(" -> ");
        let weight_str = format!("weight: $ {}", mev.weight);

        let addrs: Vec<String> = mev.pools.iter().map(|p| format!("{:x}", p.addr)).collect();
        let addrs_str = addrs.join(", ");

        println!("{} {},  {}\n{}", stats_str, path_str, weight_str, addrs_str.black());
    }
}

/// 打印池子索引链
pub fn display_pool_indexs<T>(pool_indexs: &[T], tokens: &TokenManager, str: Option<String>)
where
    T: Borrow<PoolIndex> + std::fmt::Debug,
{
    for p in pool_indexs {
        let in_token = tokens.get(&p.borrow().in_token);
        let out_token = tokens.get(&p.borrow().out_token);
        print!("{}->{} ", in_token.symbol, out_token.symbol);
    }
    print!("{:?}", str);
    println!("");
}
