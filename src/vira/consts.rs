use alloy::primitives::U256;

// commonly used U256s
pub const _U256_0X100000000: U256 = U256::from_limbs([4294967296, 0, 0, 0]);
pub const _U256_0X10000: U256 = U256::from_limbs([65536, 0, 0, 0]);
pub const _U256_0X100: U256 = U256::from_limbs([256, 0, 0, 0]);

pub const U256_10000: U256 = U256::from_limbs([10000, 0, 0, 0]);
pub const U256_1000: U256 = U256::from_limbs([1000, 0, 0, 0]);
pub const U256_618: U256 = U256::from_limbs([618, 0, 0, 0]);
pub const U256_382: U256 = U256::from_limbs([382, 0, 0, 0]);

// MAX_UINT112 = 5192296858534827628530496329220095
pub const UINT112_MAX: U256 = U256::from_limbs([
    5192296858534827628,
    530496329220095,
    0,
    0,
]);

pub const _U256_255: U256 = U256::from_limbs([255, 0, 0, 0]);
pub const _U256_192: U256 = U256::from_limbs([192, 0, 0, 0]);
pub const _U256_191: U256 = U256::from_limbs([191, 0, 0, 0]);
pub const _U256_128: U256 = U256::from_limbs([128, 0, 0, 0]);
pub const U_100: U256 = U256::from_limbs([100, 0, 0, 0]);
pub const U_60: U256 = U256::from_limbs([60, 0, 0, 0]);
pub const _U256_64: U256 = U256::from_limbs([64, 0, 0, 0]);
pub const _U256_32: U256 = U256::from_limbs([32, 0, 0, 0]);
pub const _U256_16: U256 = U256::from_limbs([16, 0, 0, 0]);
pub const _U256_8: U256 = U256::from_limbs([8, 0, 0, 0]);
pub const _U256_4: U256 = U256::from_limbs([4, 0, 0, 0]);
pub const U_2: U256 = U256::from_limbs([2, 0, 0, 0]);
pub const _U256_1: U256 = U256::from_limbs([1, 0, 0, 0]);
pub const U_MAX_U112: U256 = U256::from_limbs([u64::MAX, (1u64 << 48) - 1, 0, 0]);


// Fee constants
pub const U_666666: U256 = U256::from_limbs([666666, 0, 0, 0]);
pub const U_900004: U256 = U256::from_limbs([900004, 0, 0, 0]);
pub const U_900005: U256 = U256::from_limbs([900005, 0, 0, 0]);
pub const U_100000: U256 = U256::from_limbs([100000, 0, 0, 0]);
pub const U_2000: U256 = U256::from_limbs([2000, 0, 0, 0]);
pub const U_ZERO: U256 = U256::from_limbs([0, 0, 0, 0]);





// Uniswap V3 specific
pub const _POPULATE_TICK_DATA_STEP: u64 = 100000;
pub const _Q128: U256 = U256::from_limbs([0, 0, 1, 0]);
pub const _Q224: U256 = U256::from_limbs([0, 0, 0, 4294967296]);

// Others
pub const _U128_0X10000000000000000: u128 = 18446744073709551616;
pub const _U256_0XFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: U256 = U256::from_limbs([
    18446744073709551615,
    18446744073709551615,
    18446744073709551615,
    0,
]);
pub const _U256_0XFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: U256 =
    U256::from_limbs([18446744073709551615, 18446744073709551615, 0, 0]);

pub const UPDATE_BALANCE_RETRY : usize = 3;


// 并发限制常量
pub const MAX_CONCURRENT_TASKS: usize = 20;

pub const BATCH_SYNC : usize = 45;
//TODO: batch逻辑大于1的时候会有问题，错误率暴升
// check pools fee批量检查
pub const BATCH_CHECK_SIZE: usize = 3;
// 每批处理的MEV数量
pub const BATCH_CHECK_MEV_SIZE: usize = 3;


// 黄金分割求值的最大步骤 Algorithm constants
pub const MAX_GOLDEN_STEPS: u8 = 40;

pub const GET_LOGS_TIMEOUT: std::time::Duration = std::time::Duration::from_secs(10);