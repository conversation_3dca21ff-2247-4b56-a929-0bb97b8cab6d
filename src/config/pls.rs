use std::collections::HashMap;
use crate::{
    config::{ChainConfig, Operator, TokenConfig},
    tools::lowc,
    vira::dex::{
        factory::{DexFactory, FactoryConfig},
        uni_v2::{factory::UniV2Factory, UniV2},
        DexRouter
    }
};


pub fn new() -> ChainConfig {
    ChainConfig::new()
        .name("pls")
        //.server("https://rpc-pulsechain.g4mm4.io")
        //.server("https://rpc.pulsechain.com")
        //.server("wss://rpc.pulsechain.com")
        .server("wss://rpc-pulsechain.g4mm4.io")
        /* BOT CONTRACT ADDRESS
         * 该合约是整个系统的核心组件,负责处理所有的交易逻辑。
         * 请确保该地址正确无误,否则会导致系统无法正常工作。
         */
        .contract("******************************************")
        /*
         *
         *
         * END
         */

        
        .eth("******************************************")
        .stables(vec![
            "******************************************",
            "******************************************"
        ])
        .stable_matrix(vec![
            vec!["******************************************", "******************************************"]
        ])
        .tokens(vec![
            TokenConfig::new().name("wpls").addr("******************************************").is_eth().price(0.00005).decimals(18),
            TokenConfig::new().name("weth").addr("******************************************").is_eth().price(0.00005).decimals(18),
        ])
        //for decode swap
        .routers(vec![
            UniV2::new(FactoryConfig::new().name("plsx").addr("******************************************").fee(29)),
            UniV2::new(FactoryConfig::new().name("u6").addr("******************************************").fee(25)),
        ])
        .factories(vec![
            //UniV2Factory::new(FactoryConfig::new().name("plsxV1").addr("******************************************").fee(29)), //(60k)
            UniV2Factory::new(FactoryConfig::new().name("9mmV2").addr("******************************************").fee(25)), //(3k)
            //UniV2Factory::new(FactoryConfig::new().name("plsxV2").addr("******************************************").fee(30)),
        ])
        .operators( Operator {
            bull: HashMap::from([
                (lowc("******************************************"), lowc("04c068b8f82714cd94d726e039551f33723f7122f8682b478cbcdef7da6fde7f")),
                (lowc("0x89a8b8ae3a2a99ee6d6cb30a518705b5326816b1"), lowc("7619343a0a42476e5a8dff44a83b7283c9ca594442a66e317c4a0095fbbed8b3")),
                ]),
            pump: HashMap::from([
                (lowc("0xd8473c2e7b0c6c5c48adb3b72cf7bd79374d734b"), lowc("2b5ef1760ab306659b13b76789cbbdf9c3b21ba416e9982dc387f7473174b462")),
                (lowc("0x399e380554b106edccc7e1326528cfcf4d7da344"), lowc("5d0bff3e0b4140ca645231a3ace6c16e7e3ea7e388ec227f1aa6dafe04a47fdd")),
            ]),
        }).build()
}


mod tests {
    #[test]
    fn test_config(){
        let config = super::new();
        println!("config: {:?}", config);
    }
}