# USD 价值过滤功能实现

## 概述

在 `process_stable_pools` 函数中新增了基于 USD 价值的池过滤逻辑，用于在执行现有的池处理逻辑之前，先根据 USD 价值过滤掉价值过低的池。

## 实现位置

文件：`src/vira/status/sync.rs`

## 核心功能

### 1. 过滤函数：`filter_pools_by_usd_value`

**功能**：根据 USD 价值过滤稳定币池

**参数**：
- `stable_pools: Vec<POOL>` - 包含稳定币的池子列表
- `sm: &StatusManager` - 状态管理器，用于获取 token 信息

**返回值**：
- `Vec<POOL>` - 过滤后的池子列表

### 2. 价值计算逻辑

对于每个稳定币池：

1. **识别稳定币**：遍历池中的每个 token，找到在 `CONFIG.stables` 中配置的稳定币
2. **获取 Token 信息**：从 `StatusManager.tokens` 中获取对应的 Token 信息
3. **计算 USD 价值**：
   ```rust
   let doubled_reserve = token.reserve.saturating_mul(U256::from(2));
   pool_usd_value = token_info.usd(doubled_reserve);
   ```
   - 使用 `Token.usd(数量 * 2)` 计算价值
   - 乘以 2 是为了计算池的总价值（假设稳定币对的两边价值相等）

### 3. 过滤条件

- **最小价值阈值**：$0.001
- **过滤规则**：剔除 USD 价值小于 $0.001 的池
- **特殊处理**：如果池中找不到稳定币信息，也会被过滤掉

## 集成方式

### 修改的函数：`process_stable_pools`

**原始逻辑**：
```rust
async fn process_stable_pools(
    stable_pools: Vec<POOL>, 
    sm: &mut StatusManager, 
    contract: Arc<Contract>,
    processed_counter: Arc<AtomicUsize>
) -> Result<usize, DEXError> {
    if stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing stable pools...");
    let total = stable_pools.len();
    // ... 现有逻辑
}
```

**修改后逻辑**：
```rust
async fn process_stable_pools(
    stable_pools: Vec<POOL>, 
    sm: &mut StatusManager, 
    contract: Arc<Contract>,
    processed_counter: Arc<AtomicUsize>
) -> Result<usize, DEXError> {
    if stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing stable pools...");
    
    // 新增：根据 USD 价值过滤稳定币池
    let filtered_pools = filter_pools_by_usd_value(stable_pools, sm);
    let total = filtered_pools.len();
    
    // 使用过滤后的池子继续原有逻辑
    let chunks: Vec<Vec<POOL>> = filtered_pools
        .chunks(BATCH_CHECK_SIZE)
        .map(|chunk| chunk.to_vec())
        .collect();
    // ... 其余逻辑保持不变
}
```

## 输出信息

过滤过程会输出详细的日志信息：

```
开始根据 USD 价值过滤稳定币池...
过滤低价值池: 0x4444444444444444444444444444444444444444 (USD 价值: $0.000000)
过滤无稳定币信息池: 0x5555555555555555555555555555555555555555
USD 价值过滤完成: 保留 1 个池，过滤掉 1 个低价值池
```

## 测试

### 测试函数：`test_usd_value_filtering`

**测试场景**：
1. 创建高价值池（1000 WPLS，约 $0.05）
2. 创建低价值池（0.001 WPLS，约 $0.00000005）
3. 验证只有高价值池被保留

**测试结果**：
```
✅ USD 价值过滤测试通过：保留 1 个高价值池，过滤掉低价值池
```

## 兼容性

- ✅ **完全向后兼容**：过滤后的逻辑与现有的 `process_stable_pools` 函数逻辑完全兼容
- ✅ **性能优化**：通过提前过滤低价值池，减少后续处理的计算量
- ✅ **错误处理**：妥善处理找不到稳定币信息的情况
- ✅ **日志输出**：提供详细的过滤信息，便于调试和监控

## 配置依赖

该功能依赖以下配置：

1. **CONFIG.stables**：稳定币地址列表
2. **StatusManager.tokens**：Token 信息管理器
3. **Token.usd() 方法**：USD 价值计算方法

## 总结

此实现成功在 `process_stable_pools` 函数中增加了新的过滤逻辑，能够根据 USD 价值过滤掉价值过低的池，同时保持与现有代码的完全兼容性。过滤阈值设置为 $0.001，确保只有具有实际价值的池才会被传递给后续的处理逻辑。
